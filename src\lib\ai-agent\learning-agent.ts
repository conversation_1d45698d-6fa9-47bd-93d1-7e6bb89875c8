import ai from '@/lib/genkit';
import { googleAI } from '@genkit-ai/googleai';
import { z } from 'zod';
import {
  LearningPhase,
  LearningSession,
  AgentMessage,
  UserInteraction,
  UserResponseType,
  Curriculum,
  CurriculumStep,
  StepType
} from './types';
import { generateCurriculum, getNextStep, canAccessStep } from './curriculum-generator';

// Define the learning agent flow
export const learningAgentFlow = ai.defineFlow(
  {
    name: 'learningAgentFlow',
    inputSchema: z.object({
      userMessage: z.string(),
      sessionId: z.string(),
      currentSession: z.any().optional(),
      curriculum: z.any().optional(),
      phase: z.nativeEnum(LearningPhase).optional(),
      context: z.object({
        previousMessages: z.array(z.any()).optional(),
        userPreferences: z.any().optional(),
        learningHistory: z.any().optional(),
      }).optional(),
    }),
    outputSchema: z.object({
      response: z.string(),
      phase: z.nativeEnum(LearningPhase),
      agentMessage: z.any(),
      sessionUpdate: z.any().optional(),
      suggestedActions: z.array(z.string()).optional(),
      confidence: z.number().optional(),
    }),
  },
  async (input) => {
    const { 
      userMessage, 
      sessionId, 
      currentSession, 
      curriculum, 
      phase = LearningPhase.PLANNING,
      context 
    } = input;

    try {
      // Determine the current phase and handle accordingly
      switch (phase) {
        case LearningPhase.PLANNING:
          return await handlePlanningPhase(input);
        
        case LearningPhase.EXPLANATION:
          return await handleExplanationPhase(input);
        
        case LearningPhase.QA:
          return await handleQAPhase(input);
        
        case LearningPhase.PRACTICE:
          return await handlePracticePhase(input);
        
        case LearningPhase.ASSESSMENT:
          return await handleAssessmentPhase(input);
        
        case LearningPhase.COMPLETION:
          return await handleCompletionPhase(input);
        
        default:
          return await handleGeneralInquiry(input);
      }
    } catch (error) {
      console.error('Learning agent error:', error);
      return {
        response: "I apologize, but I encountered an error while processing your request. Let's try again.",
        phase: LearningPhase.EXPLANATION,
        agentMessage: null,
        suggestedActions: ["Try again", "Start over"],
        confidence: 0.5,
      };
    }
  }
);

// Handle planning phase - generate curriculum and present learning path
async function handlePlanningPhase(input: any) {
  const { userMessage, sessionId, context } = input;
  
  // Extract topic from user message
  const topic = extractTopic(userMessage);
  const subject = extractSubject(userMessage);
  
  if (!topic) {
    return {
      response: "I'd be happy to help you create a learning plan! Could you please specify what topic or subject you'd like to learn about?",
      phase: LearningPhase.PLANNING,
      agentMessage: {
        id: `msg-${Date.now()}`,
        type: 'guidance',
        content: "Requesting topic specification",
        phase: LearningPhase.PLANNING,
        timestamp: new Date(),
      },
      suggestedActions: [
        "I want to learn about photosynthesis",
        "Teach me about quantum physics",
        "Help me understand calculus"
      ],
      confidence: 0.8,
    };
  }

  // Generate curriculum
  const curriculumResult = await generateCurriculum({
    topic,
    subject,
    difficulty: 'intermediate',
    userGoals: context?.userPreferences?.goals || [],
    timeAvailable: context?.userPreferences?.timeAvailable || '2 hours',
    priorKnowledge: context?.learningHistory?.priorKnowledge || '',
  });

  if (!curriculumResult.success) {
    return {
      response: "I apologize, but I had trouble creating a learning plan for that topic. Could you try rephrasing or choosing a different topic?",
      phase: LearningPhase.PLANNING,
      agentMessage: {
        id: `msg-${Date.now()}`,
        type: 'guidance',
        content: "Curriculum generation failed",
        phase: LearningPhase.PLANNING,
        timestamp: new Date(),
      },
      suggestedActions: ["Try a different topic", "Rephrase request"],
      confidence: 0.6,
    };
  }

  const curriculum = curriculumResult.curriculum;
  
  // Create engaging curriculum presentation
  const response = `Great! I've created a comprehensive learning plan for **${topic}**. Here's your personalized curriculum:

## 📚 Learning Path: ${topic}

**Estimated Time:** ${curriculum.estimatedTotalTime}
**Difficulty Level:** ${curriculum.difficulty}

### 🎯 Learning Objectives:
${curriculum.learningObjectives.map((obj: string, i: number) => `${i + 1}. ${obj}`).join('\n')}

### 📋 Curriculum Steps (${curriculum.steps.length} steps):
${curriculum.steps.map((step: CurriculumStep, index: number) => 
  `${index + 1}. **${step.title}** (${step.estimatedTime}) - ${step.description}`
).join('\n')}

### 🚀 Ready to Start?
I'll guide you through each step with explanations, examples, and interactive elements. We'll start with the introduction and build your knowledge step by step.

Do you have any questions about this curriculum, or should we proceed with the first lesson?`;

  return {
    response,
    phase: LearningPhase.EXPLANATION,
    agentMessage: {
      id: `msg-${Date.now()}`,
      type: 'curriculum',
      content: response,
      phase: LearningPhase.EXPLANATION,
      metadata: {
        curriculumId: curriculum.id,
        suggestedActions: [
          "Proceed with first lesson",
          "I have a question about the curriculum",
          "Adjust difficulty level"
        ],
        confidence: 0.9,
      },
      timestamp: new Date(),
    },
    sessionUpdate: {
      curriculumId: curriculum.id,
      currentPhase: LearningPhase.EXPLANATION,
      currentStepIndex: 0,
      progress: 0,
      curriculum,
    },
    suggestedActions: [
      "Proceed with first lesson",
      "I have a question about the curriculum",
      "Adjust difficulty level"
    ],
    confidence: 0.95,
  };
}

// Handle explanation phase - provide detailed explanations of current step
async function handleExplanationPhase(input: any) {
  const { userMessage, currentSession, curriculum, context } = input;
  
  if (!curriculum || !currentSession) {
    return {
      response: "I need to generate a learning plan first. What would you like to learn about?",
      phase: LearningPhase.PLANNING,
      agentMessage: {
        id: `msg-${Date.now()}`,
        type: 'guidance',
        content: "Redirecting to planning phase",
        phase: LearningPhase.PLANNING,
        timestamp: new Date(),
      },
      suggestedActions: ["I want to learn about photosynthesis", "Teach me about quantum physics"],
      confidence: 0.8,
    };
  }

  const currentStep = curriculum.steps[currentSession.currentStepIndex];
  if (!currentStep) {
    return {
      response: "It seems we've completed all the steps in this curriculum. Great job!",
      phase: LearningPhase.COMPLETION,
      agentMessage: {
        id: `msg-${Date.now()}`,
        type: 'feedback',
        content: "Curriculum completed",
        phase: LearningPhase.COMPLETION,
        timestamp: new Date(),
      },
      suggestedActions: ["Start a new topic", "Review what I learned"],
      confidence: 0.9,
    };
  }

  // Check if user is asking a question about the current step
  if (isQuestionAboutCurrentStep(userMessage, currentStep)) {
    return await handleQAPhase(input);
  }

  // Generate explanation for current step
  const explanation = await generateStepExplanation(currentStep, curriculum, context);
  
  const response = `${explanation}

---

**Do you have any questions about this concept, or should I proceed with the next part of the lesson?**`;

  return {
    response,
    phase: LearningPhase.EXPLANATION,
    agentMessage: {
      id: `msg-${Date.now()}`,
      type: 'explanation',
      content: explanation,
      phase: LearningPhase.EXPLANATION,
      stepId: currentStep.id,
      metadata: {
        suggestedActions: [
          "I have a question",
          "Proceed to next step",
          "Can you give me an example?"
        ],
        confidence: 0.9,
        nextStepHint: getNextStep(curriculum, currentSession.currentStepIndex)?.title || "Moving to next step",
      },
      timestamp: new Date(),
    },
    suggestedActions: [
      "I have a question",
      "Proceed to next step",
      "Can you give me an example?"
    ],
    confidence: 0.9,
  };
}

// Handle Q&A phase - answer user questions
async function handleQAPhase(input: any) {
  const { userMessage, currentSession, curriculum, context } = input;
  
  if (!curriculum || !currentSession) {
    return await handleGeneralInquiry(input);
  }

  const currentStep = curriculum.steps[currentSession.currentStepIndex];
  const questionContext = currentStep ? 
    `The user is asking about: "${currentStep.title}". Current step content: ${currentStep.content}` :
    'General question about the learning topic';

  const systemPrompt = `You are a patient and knowledgeable AI tutor. The user is asking a question during their learning session.

Context: ${questionContext}
User Question: ${userMessage}

Instructions:
1. Answer the question clearly and concisely
2. Relate it to the current learning material if applicable
3. Use examples and analogies to help understanding
4. Be encouraging and supportive
5. After answering, ask if they understood or need clarification
6. Keep the response focused on their question

Your response should be helpful, educational, and engaging.`;

  const response = await ai.generate({
    model: googleAI.model('gemini-2.5-flash'),
    messages: [
      {
        role: 'system' as const,
        content: [{ text: systemPrompt }],
      },
      {
        role: 'user' as const,
        content: [{ text: userMessage }],
      },
    ],
    config: {
      temperature: 0.7,
      maxOutputTokens: 1000,
    },
  });

  const answer = response.text;

  const fullResponse = `${answer}

---

Did that answer your question? If you need more clarification, feel free to ask! Otherwise, we can continue with the lesson.`;

  return {
    response: fullResponse,
    phase: LearningPhase.EXPLANATION,
    agentMessage: {
      id: `msg-${Date.now()}`,
      type: 'question',
      content: answer,
      phase: LearningPhase.QA,
      stepId: currentStep?.id,
      metadata: {
        suggestedActions: [
          "Yes, I understood",
          "Can you explain differently?",
          "Proceed with the lesson"
        ],
        confidence: 0.85,
      },
      timestamp: new Date(),
    },
    suggestedActions: [
      "Yes, I understood",
      "Can you explain differently?",
      "Proceed with the lesson"
    ],
    confidence: 0.85,
  };
}

// Handle practice phase - provide practice exercises
async function handlePracticePhase(input: any) {
  // Implementation for practice phase
  return {
    response: "Practice phase is coming soon! For now, let's continue with the explanation.",
    phase: LearningPhase.EXPLANATION,
    agentMessage: {
      id: `msg-${Date.now()}`,
      type: 'guidance',
      content: "Practice phase placeholder",
      phase: LearningPhase.EXPLANATION,
      timestamp: new Date(),
    },
    suggestedActions: ["Continue with explanation"],
    confidence: 0.7,
  };
}

// Handle assessment phase - provide quizzes and assessments
async function handleAssessmentPhase(input: any) {
  // Implementation for assessment phase
  return {
    response: "Assessment phase is coming soon! For now, let's continue with the explanation.",
    phase: LearningPhase.EXPLANATION,
    agentMessage: {
      id: `msg-${Date.now()}`,
      type: 'guidance',
      content: "Assessment phase placeholder",
      phase: LearningPhase.EXPLANATION,
      timestamp: new Date(),
    },
    suggestedActions: ["Continue with explanation"],
    confidence: 0.7,
  };
}

// Handle completion phase - celebrate completion and provide next steps
async function handleCompletionPhase(input: any) {
  const { curriculum, currentSession } = input;

  const response = `🎉 **Congratulations!** You've successfully completed your learning journey on **${curriculum?.topic || 'this topic'}**!

### 🏆 What You've Accomplished:
- Completed all ${curriculum?.steps.length || 0} learning steps
- Built a solid understanding of the subject
- Developed practical knowledge you can apply

### 🚀 Next Steps:
- **Practice**: Apply what you've learned in real-world scenarios
- **Explore**: Dive deeper into advanced topics
- **Share**: Teach others to reinforce your understanding
- **Review**: Revisit key concepts periodically

### 📚 Continue Your Learning:
Would you like to:
1. Start a new topic?
2. Review what you've learned?
3. Explore related subjects?

Great job on your dedication to learning! 🌟`;

  return {
    response,
    phase: LearningPhase.COMPLETION,
    agentMessage: {
      id: `msg-${Date.now()}`,
      type: 'feedback',
      content: response,
      phase: LearningPhase.COMPLETION,
      metadata: {
        suggestedActions: [
          "Start a new topic",
          "Review what I learned",
          "Explore related subjects"
        ],
        confidence: 0.95,
      },
      timestamp: new Date(),
    },
    suggestedActions: [
      "Start a new topic",
      "Review what I learned",
      "Explore related subjects"
    ],
    confidence: 0.95,
  };
}

// Handle general inquiry - when no specific phase is active
async function handleGeneralInquiry(input: any) {
  const { userMessage, context } = input;

  const systemPrompt = `You are a helpful AI learning assistant. The user is asking a general question or starting a new learning session.

User Message: ${userMessage}
Context: ${JSON.stringify(context)}

Instructions:
1. If they want to learn something new, guide them to specify a topic
2. If they have a general question, answer it helpfully
3. Be encouraging and supportive
4. Suggest starting a learning session if appropriate
5. Keep your response concise and engaging`;

  const response = await ai.generate({
    model: googleAI.model('gemini-2.5-flash'),
    messages: [
      {
        role: 'system' as const,
        content: [{ text: systemPrompt }],
      },
      {
        role: 'user' as const,
        content: [{ text: userMessage }],
      },
    ],
    config: {
      temperature: 0.7,
      maxOutputTokens: 800,
    },
  });

  return {
    response: response.text,
    phase: LearningPhase.PLANNING,
    agentMessage: {
      id: `msg-${Date.now()}`,
      type: 'guidance',
      content: response.text,
      phase: LearningPhase.PLANNING,
      timestamp: new Date(),
    },
    suggestedActions: [
      "I want to learn about [topic]",
      "Help me understand [concept]",
      "Create a learning plan"
    ],
    confidence: 0.8,
  };
}

// Helper functions
function extractTopic(message: string): string | null {
  const patterns = [
    /(?:learn about|teach me|explain|what is|tell me about)\s+([^.!?]+)/i,
    /(?:i want to learn|i'd like to understand)\s+([^.!?]+)/i,
  ];

  for (const pattern of patterns) {
    const match = message.match(pattern);
    if (match && match[1]) {
      return match[1].trim();
    }
  }

  return null;
}

function extractSubject(message: string): string {
  const subjects = ['science', 'math', 'physics', 'chemistry', 'biology', 'history', 'literature', 'programming'];
  const messageLower = message.toLowerCase();
  
  for (const subject of subjects) {
    if (messageLower.includes(subject)) {
      return subject;
    }
  }

  return 'general';
}

function isQuestionAboutCurrentStep(message: string, step: CurriculumStep): boolean {
  const questionWords = ['what', 'how', 'why', 'when', 'where', 'who', 'can', 'could', 'would', 'should', 'is', 'are'];
  const messageLower = message.toLowerCase();
  
  // Check if it's a question
  const isQuestion = questionWords.some(word => messageLower.includes(word)) || messageLower.includes('?');
  
  if (!isQuestion) return false;

  // Check if question relates to current step
  const stepKeywords = step.title.toLowerCase().split(' ');
  return stepKeywords.some(keyword => messageLower.includes(keyword));
}

async function generateStepExplanation(step: CurriculumStep, curriculum: Curriculum, context: any): Promise<string> {
  const systemPrompt = `You are an expert educator. Your task is to explain the current learning step in a clear, engaging, and educational manner.

Step Information:
- Title: ${step.title}
- Description: ${step.description}
- Type: ${step.type}
- Difficulty: ${step.difficulty}
- Content: ${step.content}

Curriculum Context:
- Topic: ${curriculum.topic}
- Subject: ${curriculum.subject}
- Overall Difficulty: ${curriculum.difficulty}

Instructions:
1. Start with a clear, engaging introduction to the step
2. Explain the concept in detail, using simple language
3. Use examples, analogies, or real-world applications
4. Break down complex ideas into digestible parts
5. Maintain an encouraging and supportive tone
6. Keep the explanation appropriate for the difficulty level
7. End with a natural transition to questions or next steps

Your explanation should be comprehensive yet accessible, and should make the learner feel confident about understanding the material.`;

  const response = await ai.generate({
    model: googleAI.model('gemini-2.5-flash-001'),
    messages: [
      {
        role: 'system' as const,
        content: [{ text: systemPrompt }],
      },
      {
        role: 'user' as const,
        content: [{ text: `Please explain ${step.title} in a way that's easy to understand and engaging.` }],
      },
    ],
    config: {
      temperature: 0.7,
      maxOutputTokens: 1500,
    },
  });

  return response.text;
}

// Export flow runner
export const runLearningAgent = (input: any) => learningAgentFlow(input);